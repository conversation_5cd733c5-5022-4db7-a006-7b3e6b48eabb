#!/usr/bin/env ts-node

/**
 * <PERSON><PERSON><PERSON> to fix specific worksheet synchronization issues between PostgreSQL and MongoDB
 * This script uses the new syncMongoDBWithPostgreSQL method to fix data inconsistencies
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { WorksheetQuestionService, UserContext } from '../src/modules/worksheet/services/worksheet-question.service';
import { EUserRole } from '../src/modules/user/dto/create-user.dto';
import { Logger } from '@nestjs/common';

const logger = new Logger('FixWorksheetSync');

class FixWorksheetSyncScript {
  private app: any;
  private worksheetQuestionService: WorksheetQuestionService;

  async initialize(): Promise<void> {
    logger.log('🚀 Initializing NestJS application...');
    
    this.app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error', 'warn', 'log'],
    });

    this.worksheetQuestionService = this.app.get(WorksheetQuestionService);
    
    logger.log('✅ Application initialized successfully');
  }

  async cleanup(): Promise<void> {
    if (this.app) {
      await this.app.close();
      logger.log('🔌 Application closed');
    }
  }

  /**
   * Fix synchronization for a specific worksheet
   */
  async fixWorksheetSync(worksheetId: string, dryRun: boolean = true): Promise<void> {
    logger.log(`\n🔧 ${dryRun ? 'DRY RUN - ' : ''}Fixing worksheet sync: ${worksheetId}\n`);

    // Create a system user context for the operation
    const systemUser: UserContext = {
      sub: 'system-sync-script',
      email: '<EMAIL>',
      role: EUserRole.ADMIN,
      schoolId: null
    };

    try {
      const result = await this.worksheetQuestionService.syncMongoDBWithPostgreSQL(
        worksheetId,
        systemUser,
        { dryRun }
      );

      logger.log(`\n📊 Sync Result:`);
      logger.log(`   Success: ${result.success}`);
      logger.log(`   Summary: ${result.summary}`);
      
      if (result.changes.length > 0) {
        logger.log(`\n📝 Changes ${dryRun ? 'that would be made' : 'made'}:`);
        result.changes.forEach((change, index) => {
          logger.log(`   ${index + 1}. [${change.action.toUpperCase()}] ${change.questionId}: ${change.details}`);
        });
      } else {
        logger.log(`\n✅ No changes needed - databases are already in sync`);
      }

    } catch (error) {
      logger.error(`❌ Failed to sync worksheet ${worksheetId}:`, error.message);
      throw error;
    }
  }

  /**
   * Substitute a specific question ID with another
   */
  async substituteQuestion(
    worksheetId: string,
    oldQuestionId: string,
    newQuestionId: string
  ): Promise<void> {
    logger.log(`\n🔄 Substituting question ${oldQuestionId} with ${newQuestionId} in worksheet ${worksheetId}\n`);

    // Create a system user context for the operation
    const systemUser: UserContext = {
      sub: 'system-sync-script',
      email: '<EMAIL>',
      role: EUserRole.ADMIN,
      schoolId: null
    };

    try {
      await this.worksheetQuestionService.substituteQuestionInWorksheet(
        worksheetId,
        oldQuestionId,
        newQuestionId,
        systemUser
      );

      logger.log(`✅ Successfully substituted question ${oldQuestionId} with ${newQuestionId}`);

    } catch (error) {
      logger.error(`❌ Failed to substitute question:`, error.message);
      throw error;
    }
  }
}

async function main() {
  const script = new FixWorksheetSyncScript();
  
  try {
    await script.initialize();

    const args = process.argv.slice(2);
    const command = args[0];
    const worksheetId = args[1];

    if (!command || !worksheetId) {
      logger.error('Usage:');
      logger.error('  npm run fix-worksheet-sync sync <worksheetId> [--dry-run]');
      logger.error('  npm run fix-worksheet-sync substitute <worksheetId> <oldQuestionId> <newQuestionId>');
      logger.error('');
      logger.error('Examples:');
      logger.error('  npm run fix-worksheet-sync sync ************************************ --dry-run');
      logger.error('  npm run fix-worksheet-sync sync ************************************');
      logger.error('  npm run fix-worksheet-sync substitute ************************************ 3b2fd2b6-1baa-4cc2-a46c-4fb0237e42f7 9b833935-3152-4fe6-afab-9679da3e4eb9');
      process.exit(1);
    }

    if (command === 'sync') {
      const dryRun = args.includes('--dry-run');
      await script.fixWorksheetSync(worksheetId, dryRun);
    } else if (command === 'substitute') {
      const oldQuestionId = args[2];
      const newQuestionId = args[3];
      
      if (!oldQuestionId || !newQuestionId) {
        logger.error('❌ Both oldQuestionId and newQuestionId are required for substitute command');
        process.exit(1);
      }
      
      await script.substituteQuestion(worksheetId, oldQuestionId, newQuestionId);
    } else {
      logger.error(`❌ Unknown command: ${command}`);
      process.exit(1);
    }

  } catch (error) {
    logger.error('❌ Script failed:', error.message);
    process.exit(1);
  } finally {
    await script.cleanup();
  }
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}
